<?php
session_start();
include_once '../config/db_config.php';

// 检查用户是否已登录，否则重定向到登录页面
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header("Location: login.php");
    exit;
}

$success_message = '';
$error_message = '';

// 处理表单提交
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    // 验证输入
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error_message = "所有字段都是必填的。";
    } elseif ($new_password !== $confirm_password) {
        $error_message = "新密码和确认密码不匹配。";
    } elseif (strlen($new_password) < 6) {
        $error_message = "新密码长度至少为6位。";
    } else {
        // 验证当前密码
        $admin_id = $_SESSION['admin_id'];
        $stmt = $mysqli->prepare("SELECT password FROM admin_users WHERE id = ?");
        if ($stmt) {
            $stmt->bind_param("i", $admin_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows == 1) {
                $admin = $result->fetch_assoc();
                if ($admin['password'] === md5($current_password)) {
                    // 当前密码正确，更新密码
                    $new_password_hash = md5($new_password);
                    $update_stmt = $mysqli->prepare("UPDATE admin_users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                    if ($update_stmt) {
                        $update_stmt->bind_param("si", $new_password_hash, $admin_id);
                        if ($update_stmt->execute()) {
                            $success_message = "密码修改成功！";
                        } else {
                            $error_message = "密码修改失败，请重试。";
                        }
                        $update_stmt->close();
                    } else {
                        $error_message = "数据库操作失败。";
                    }
                } else {
                    $error_message = "当前密码不正确。";
                }
            } else {
                $error_message = "用户信息不存在。";
            }
            $stmt->close();
        } else {
            $error_message = "数据库查询失败。";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改密码 - 问卷系统</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 500px;
            margin: 50px auto;
            background-color: #ffffff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .container h2 {
            color: #0050b3;
            margin-bottom: 30px;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
        }
        .form-group input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 14px;
        }
        .form-group input[type="password"]:focus {
            border-color: #007bff;
            outline: none;
        }
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            text-align: center;
            flex: 1;
            transition: background-color 0.3s ease;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .success-message {
            color: #28a745;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .error-message {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .password-requirements {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>修改密码</h2>
        
        <?php if (!empty($success_message)): ?>
            <div class="success-message"><?php echo htmlspecialchars($success_message); ?></div>
        <?php endif; ?>
        
        <?php if (!empty($error_message)): ?>
            <div class="error-message"><?php echo htmlspecialchars($error_message); ?></div>
        <?php endif; ?>
        
        <form action="change_password.php" method="post">
            <div class="form-group">
                <label for="current_password">当前密码:</label>
                <input type="password" id="current_password" name="current_password" required>
            </div>
            
            <div class="form-group">
                <label for="new_password">新密码:</label>
                <input type="password" id="new_password" name="new_password" required>
                <div class="password-requirements">密码长度至少为6位</div>
            </div>
            
            <div class="form-group">
                <label for="confirm_password">确认新密码:</label>
                <input type="password" id="confirm_password" name="confirm_password" required>
            </div>
            
            <div class="btn-group">
                <button type="submit" class="btn btn-primary">修改密码</button>
                <a href="dashboard.php" class="btn btn-secondary">返回后台</a>
            </div>
        </form>
    </div>
</body>
</html>
