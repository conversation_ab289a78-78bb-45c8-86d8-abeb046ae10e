<?php
// 确保此文件被 dashboard.php 正确包含
if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME'])) {
    echo "此页面不能直接访问。";
    exit;
}
// global $mysqli; // 如果需要数据库操作

$error_message = '';
$success_message = '';

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['questionnaire_title'])) {
    $title = trim($_POST['questionnaire_title']);
    $submission_limit = isset($_POST['submission_limit']) && $_POST['submission_limit'] !== '' ? (int)$_POST['submission_limit'] : null;

    if (empty($title)) {
        $error_message = "问卷标题不能为空。";
    } elseif ($submission_limit !== null && $submission_limit <= 0) {
        $error_message = "回收份数限制必须是正整数。";
    } else {
        // 实际项目中，这里会将标题保存到数据库，并获取新问卷的ID
        // 为了演示，我们先将标题存入 session，并模拟一个ID
        // 生成一个唯一的10位数ID (这里用时间戳+随机数简化)
        $questionnaire_id = substr(time() . rand(100,999), 0, 10); // 简单示例

        $_SESSION['current_questionnaire_id'] = $questionnaire_id;
        $_SESSION['current_questionnaire_title'] = $title;
        $_SESSION['current_questionnaire_submission_limit'] = $submission_limit;

        // 清除可能存在的旧问卷问题数据
        unset($_SESSION['current_questionnaire_questions']);

        // 跳转到问题添加页面
        // 注意：dashboard.php 会根据 session 中的 current_questionnaire_id 来加载 create_questionnaire_questions.php
        header("Location: dashboard.php?page=create_questionnaire_questions");
        exit;
    }
}
?>
<style>
    .form-container {
        background-color: #fff;
        padding: 25px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        max-width: 600px;
        margin: 20px auto;
    }
    .form-container h1 {
        color: #0050b3;
        margin-bottom: 20px;
        font-size: 22px;
    }
    .form-container label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        color: #333;
    }
    .form-container input[type="text"],
    .form-container input[type="number"] {
        width: 100%;
        padding: 10px;
        margin-bottom: 5px;
        border: 1px solid #b3d9ff;
        border-radius: 4px;
        box-sizing: border-box;
    }
    .form-container input[type="text"]:focus,
    .form-container input[type="number"]:focus {
        border-color: #007bff;
        outline: none;
    }
    .form-container button {
        background-color: #007bff;
        color: white;
        padding: 10px 18px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.3s ease;
    }
    .form-container button:hover {
        background-color: #0056b3;
    }
    .message {
        padding: 10px;
        margin-bottom: 15px;
        border-radius: 4px;
        font-size: 14px;
    }
    .error-message {
        background-color: #ffebee;
        color: #c62828;
        border: 1px solid #ef9a9a;
    }
    .success-message {
        background-color: #e8f5e9;
        color: #2e7d32;
        border: 1px solid #a5d6a7;
    }
</style>

<div class="form-container">
    <h1>创建新问卷 - 输入标题</h1>

    <?php if (!empty($error_message)): ?>
        <div class="message error-message"><?php echo $error_message; ?></div>
    <?php endif; ?>
    <?php if (!empty($success_message)): ?>
        <div class="message success-message"><?php echo $success_message; ?></div>
    <?php endif; ?>

    <form action="dashboard.php?page=create_questionnaire_title" method="post">
        <label for="questionnaire_title">问卷标题:</label>
        <input type="text" id="questionnaire_title" name="questionnaire_title" value="<?php echo isset($_SESSION['current_questionnaire_title']) ? htmlspecialchars($_SESSION['current_questionnaire_title']) : ''; ?>" required>

        <label for="submission_limit">回收份数限制:</label>
        <input type="number" id="submission_limit" name="submission_limit" min="1" placeholder="留空表示无限制" value="<?php echo isset($_SESSION['current_questionnaire_submission_limit']) ? $_SESSION['current_questionnaire_submission_limit'] : ''; ?>">
        <small style="color: #666; font-size: 12px; display: block; margin-bottom: 20px;">设置问卷最多可以回收多少份，留空表示无限制</small>

        <button type="submit">下一步：添加问题</button>
    </form>
</div>
