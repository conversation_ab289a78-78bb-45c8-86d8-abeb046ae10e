<?php
// Ensure this file is included by dashboard.php and user is logged in
if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME']) || !isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME'])) {
        echo "此页面不能直接访问。";
    }
    exit;
}

global $mysqli; 

$questionnaire_id = $_GET['id'] ?? null;
$questionnaire_title = '';
$current_questions = []; 

$error_message = '';
$success_message = '';

if (!$questionnaire_id) {
    $_SESSION['message'] = ['type' => 'error', 'text' => '无效的问卷ID。'];
    echo "<p style='color:red;'>错误：未提供问卷ID。</p>";
    return; 
}

// --- Initialize or Load Questions into Session ---
if (!isset($_SESSION['editing_questionnaire_id']) || $_SESSION['editing_questionnaire_id'] !== $questionnaire_id) {
    $_SESSION['editing_questionnaire_id'] = $questionnaire_id;
    $_SESSION['editing_questions'] = []; 
    $stmt_load_q = $mysqli->prepare("SELECT id, type, label, options, sort_order, answer_limit FROM questions WHERE questionnaire_id = ? ORDER BY sort_order ASC");
    if ($stmt_load_q) {
        $stmt_load_q->bind_param("s", $questionnaire_id);
        $stmt_load_q->execute();
        $result_load_q = $stmt_load_q->get_result();
        while ($row = $result_load_q->fetch_assoc()) {
            if (!empty($row['options'])) {
                $row['options'] = json_decode($row['options'], true);
            }
            $_SESSION['editing_questions'][] = $row;
        }
        $stmt_load_q->close();
    } else {
        $error_message = "加载现有问题失败: " . $mysqli->error;
    }
}
$current_questions = &$_SESSION['editing_questions']; 

// --- State for editing a specific question ---
$editing_question_index = null; 
$editing_question_data = null;  

if (isset($_GET['action']) && $_GET['action'] == 'edit_existing_question' && isset($_GET['q_idx'])) {
    $q_idx = (int)$_GET['q_idx'];
    if (isset($current_questions[$q_idx])) {
        $editing_question_index = $q_idx;
        $editing_question_data = $current_questions[$q_idx];
    } else {
        $error_message = "要编辑的问题未找到。";
    }
}

// --- Handle Question Reordering ---
if (isset($_GET['action']) && ($_GET['action'] == 'move_question_up' || $_GET['action'] == 'move_question_down') && isset($_GET['q_idx'])) {
    $q_idx_to_move = (int)$_GET['q_idx'];
    $direction = $_GET['action'];

    if (isset($current_questions[$q_idx_to_move])) {
        $item_to_move = $current_questions[$q_idx_to_move];
        if ($direction == 'move_question_up' && $q_idx_to_move > 0) {
            // Swap with previous item
            $previous_item = $current_questions[$q_idx_to_move - 1];
            $current_questions[$q_idx_to_move - 1] = $item_to_move;
            $current_questions[$q_idx_to_move] = $previous_item;
            $_SESSION['message'] = ['type' => 'success', 'text' => '问题顺序已调整。'];
        } elseif ($direction == 'move_question_down' && $q_idx_to_move < (count($current_questions) - 1)) {
            // Swap with next item
            $next_item = $current_questions[$q_idx_to_move + 1];
            $current_questions[$q_idx_to_move + 1] = $item_to_move;
            $current_questions[$q_idx_to_move] = $next_item;
            $_SESSION['message'] = ['type' => 'success', 'text' => '问题顺序已调整。'];
        }
    }
    // Redirect to clean URL after action
    header("Location: dashboard.php?page=edit_questionnaire&id=" . $questionnaire_id);
    exit;
}


// --- Handle Title and Submission Limit Update ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_title_action'])) {
    $new_title = trim($_POST['questionnaire_title_edit']);
    $new_submission_limit = isset($_POST['submission_limit_edit']) && $_POST['submission_limit_edit'] !== '' ? (int)$_POST['submission_limit_edit'] : null;

    if (empty($new_title)) {
        $error_message = '问卷标题不能为空。';
    } elseif ($new_submission_limit !== null && $new_submission_limit <= 0) {
        $error_message = '回收份数限制必须是正整数。';
    } else {
        $stmt_title = $mysqli->prepare("UPDATE questionnaires SET title = ?, submission_limit = ? WHERE id = ?");
        if ($stmt_title) {
            $stmt_title->bind_param("sis", $new_title, $new_submission_limit, $questionnaire_id);
            if ($stmt_title->execute()) {
                $success_message = '问卷信息已成功更新。';
                $questionnaire_title = $new_title;
                $questionnaire_submission_limit = $new_submission_limit;
            } else {
                $error_message = '更新问卷信息失败: ' . $stmt_title->error;
            }
            $stmt_title->close();
        } else {
            $error_message = '准备更新操作失败: ' . $mysqli->error;
        }
    }
}

// --- Fetch current questionnaire title and submission_limit ---
$stmt_select_title = $mysqli->prepare("SELECT title, submission_limit FROM questionnaires WHERE id = ?");
if ($stmt_select_title) {
    $stmt_select_title->bind_param("s", $questionnaire_id);
    $stmt_select_title->execute();
    $result_title = $stmt_select_title->get_result();
    if ($row_title = $result_title->fetch_assoc()) {
        $questionnaire_title = $row_title['title'];
        $questionnaire_submission_limit = $row_title['submission_limit'];
    } else {
        $error_message = '未找到指定的问卷。';
        unset($_SESSION['editing_questionnaire_id'], $_SESSION['editing_questions']);
        echo "<p style='color:red;'>错误：未找到问卷。</p>"; return;
    }
    $stmt_select_title->close();
} else {
    $error_message = '获取问卷信息失败: ' . $mysqli->error;
}

// --- Handle Add or Update Question ---
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['add_question_edit']) || isset($_POST['update_question_edit'])) {
        $question_type = $_POST['question_type_edit'] ?? '';
        $question_label = trim($_POST['question_label_edit'] ?? '');
        $question_answer_limit = isset($_POST['question_answer_limit_edit']) && $_POST['question_answer_limit_edit'] !== '' ? (int)$_POST['question_answer_limit_edit'] : null;
        $options_text = $_POST['options_text_edit'] ?? [];
        $options_limit = $_POST['options_limit_edit'] ?? [];
        $q_idx_to_update = isset($_POST['editing_question_idx']) ? (int)$_POST['editing_question_idx'] : null;
        $is_update_action = isset($_POST['update_question_edit']);

        if (empty($question_type)) {
            $error_message = "请选择一个题型。";
        } elseif (empty($question_label) && !in_array($question_type, ['name', 'id_card', 'phone'])) {
            $error_message = "题目内容不能为空。";
        } elseif ($question_answer_limit !== null && $question_answer_limit <= 0) {
            $error_message = "相同答案数量限制必须是正整数。";
        } else {
            // 对于身份证和手机号码，强制设置限额为1（唯一性验证）
            if (in_array($question_type, ['id_card', 'phone'])) {
                $question_answer_limit = 1;
            }

            $question_data = ['type' => $question_type, 'label' => $question_label, 'answer_limit' => $question_answer_limit];
            if ($is_update_action && isset($current_questions[$q_idx_to_update]['id'])) {
                 $question_data['id'] = $current_questions[$q_idx_to_update]['id'];
            } else {
                 $question_data['id'] = 'new_' . uniqid();
            }
            switch ($question_type) {
                case 'name': $question_data['label'] = !empty($question_label) ? $question_label : '姓名'; break;
                case 'id_card':
                    $question_data['label'] = !empty($question_label) ? $question_label : '身份证号码';
                    $question_data['answer_limit'] = 1; // 强制唯一性
                    break;
                case 'phone':
                    $question_data['label'] = !empty($question_label) ? $question_label : '手机';
                    $question_data['answer_limit'] = 1; // 强制唯一性
                    break;
                case 'single_choice':
                    $valid_options = [];
                    for ($i = 0; $i < count($options_text); $i++) {
                        $text = trim($options_text[$i] ?? '');
                        $limit = isset($options_limit[$i]) && $options_limit[$i] !== '' ? (int)$options_limit[$i] : null;

                        if (!empty($text)) {
                            if ($limit !== null && $limit <= 0) {
                                $error_message = "选项限制数量必须是正整数。";
                                break;
                            }
                            $valid_options[] = ['text' => $text, 'limit' => $limit];
                        }
                    }
                    if (empty($error_message)) {
                        if (count($valid_options) < 2) {
                            $error_message = "单选题至少需要2个有效选项。";
                        } else {
                            $question_data['options'] = $valid_options;
                        }
                    }
                    break;
            }
            if (empty($error_message)) {
                if ($is_update_action && $q_idx_to_update !== null && isset($current_questions[$q_idx_to_update])) {
                    $current_questions[$q_idx_to_update] = array_merge($current_questions[$q_idx_to_update], $question_data);
                    $_SESSION['message'] = ['type' => 'success', 'text' => "问题“" . htmlspecialchars($question_data['label']) . "”已更新。"];
                    header("Location: dashboard.php?page=edit_questionnaire&id=" . $questionnaire_id . "&question_updated=1");
                    exit;
                } else {
                    $current_questions[] = $question_data;
                    $success_message = "问题“" . htmlspecialchars($question_data['label']) . "”已添加到列表。";
                }
                $_POST = []; 
            }
        }
    }
}

// --- Handle Delete Question from Session ---
if (isset($_GET['action']) && $_GET['action'] == 'delete_question_edit' && isset($_GET['q_idx'])) {
    $q_idx_to_delete = (int)$_GET['q_idx'];
    if (isset($current_questions[$q_idx_to_delete])) {
        unset($current_questions[$q_idx_to_delete]);
        $current_questions = array_values($current_questions); 
        $_SESSION['message'] = ['type' => 'success', 'text' => '问题已从列表删除。'];
    } else {
        $_SESSION['message'] = ['type' => 'error', 'text' => '未找到要删除的问题。'];
    }
    header("Location: dashboard.php?page=edit_questionnaire&id=" . $questionnaire_id);
    exit;
}

// --- Handle Save Entire Questionnaire (Updates to DB) ---
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['save_questionnaire_edit'])) {
    if (empty($error_message)) {
        $mysqli->begin_transaction();
        try {
            $stmt_delete_old = $mysqli->prepare("DELETE FROM questions WHERE questionnaire_id = ?");
            if (!$stmt_delete_old) throw new Exception("Prepare delete old questions failed: " . $mysqli->error);
            $stmt_delete_old->bind_param("s", $questionnaire_id);
            if (!$stmt_delete_old->execute()) throw new Exception("Execute delete old questions failed: " . $stmt_delete_old->error);
            $stmt_delete_old->close();

            foreach ($current_questions as $index => $q_data) {
                $q_actual_id = (strpos($q_data['id'], 'new_') === 0 || empty($q_data['id'])) ? ('q_' . uniqid()) : $q_data['id'];
                $q_type = $q_data['type'];
                $q_label = $q_data['label'];
                $q_answer_limit = isset($q_data['answer_limit']) ? $q_data['answer_limit'] : null;
                $q_options_json = (isset($q_data['options']) && is_array($q_data['options'])) ? json_encode($q_data['options'], JSON_UNESCAPED_UNICODE) : null;
                $q_order = $index + 1;

                $stmt_q_insert = $mysqli->prepare("INSERT INTO questions (id, questionnaire_id, type, label, options, sort_order, answer_limit, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
                if (!$stmt_q_insert) throw new Exception("Prepare insert question failed: " . $mysqli->error);
                $stmt_q_insert->bind_param("sssssii", $q_actual_id, $questionnaire_id, $q_type, $q_label, $q_options_json, $q_order, $q_answer_limit);
                if (!$stmt_q_insert->execute()) throw new Exception("Execute insert question ('{$q_label}') failed: " . $stmt_q_insert->error);
                $stmt_q_insert->close();
            }
            $mysqli->commit();
            $_SESSION['message'] = ['type' => 'success', 'text' => "问卷 (ID: " . htmlspecialchars($questionnaire_id) . ") 的所有更改已成功保存！"];
            unset($_SESSION['editing_questionnaire_id'], $_SESSION['editing_questions']); 
            header("Location: dashboard.php?page=all_questionnaires"); 
            exit;
        } catch (Exception $e) {
            $mysqli->rollback();
            $error_message = "保存问卷更改失败: " . $e->getMessage();
        }
    }
}

// Display session messages
if (isset($_SESSION['message'])) {
    if ($_SESSION['message']['type'] == 'success') $success_message = $_SESSION['message']['text'];
    else $error_message = $_SESSION['message']['text'];
    unset($_SESSION['message']);
}
if (isset($_GET['question_updated'])) $success_message = "问题已更新。";

$current_selected_type_for_form = $editing_question_data['type'] ?? ($_GET['type'] ?? '');
$form_label_value = $editing_question_data['label'] ?? '';
$form_options_value = $editing_question_data['options'] ?? [];
$form_answer_limit_value = $editing_question_data['answer_limit'] ?? null;
?>
<style>
    .form-container { background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); max-width: 800px; margin: 20px auto; }
    .question-editor-container { display: flex; gap: 20px; margin-top:20px; }
    .question-types-sidebar { width: 200px; padding: 15px; background-color: #f8f9fa; border-radius: 6px; border: 1px solid #e9ecef; height: fit-content; }
    .question-types-sidebar h3 { margin-top: 0; margin-bottom: 15px; font-size: 16px; color: #0050b3; border-bottom: 1px solid #dee2e6; padding-bottom: 10px; }
    .question-types-sidebar ul { list-style: none; padding: 0; margin: 0; }
    .question-types-sidebar li a { display: block; padding: 10px 12px; text-decoration: none; color: #007bff; border-radius: 4px; margin-bottom: 5px; transition: background-color 0.2s ease; }
    .question-types-sidebar li a:hover, .question-types-sidebar li a.active { background-color: #007bff; color: white; }
    .question-settings-main { flex-grow: 1; padding: 20px; background-color: #fff; border-radius: 6px; box-shadow: 0 1px 5px rgba(0,0,0,0.08); }
    .form-section { margin-bottom: 20px; }
    .form-section label { display: block; margin-bottom: 6px; font-weight: bold; font-size: 14px; }
    .form-section input[type="text"], .form-section input[type="number"], .form-section textarea { width: 100%; padding: 9px; border: 1px solid #ced4da; border-radius: 4px; box-sizing: border-box; }
    .options-container .option-item { display: flex; align-items: center; margin-bottom: 8px; }
    .options-container .option-item input[type="text"] { flex: 2; margin-right: 8px; }
    .options-container .option-item input[type="number"] { flex: 1; margin-right: 8px; }
    .btn-add-option, .btn-remove-option, .btn-action { padding: 8px 15px; font-size: 14px; border-radius: 4px; cursor: pointer; border: 1px solid transparent; margin-right: 5px; }
    .btn-add-option { background-color: #28a745; color: white; }
    .btn-remove-option { background-color: #dc3545; color: white; }
    .btn-action { background-color: #007bff; color: white; } 
    .btn-save-all { background-color: #17a2b8; color: white; float: right; }
    .btn-secondary-custom { 
        background-color: #6c757d; 
        color: white; 
        padding: 8px 15px; 
        font-size: 14px; 
        border-radius: 4px; 
        text-decoration: none; 
        border: 1px solid transparent;
        display: inline-block; /* To behave like a button */
    }
    .btn-secondary-custom:hover {
        background-color: #5a6268;
        color: white;
        text-decoration: none;
    }
    .added-questions-list { margin-top: 30px; }
    .added-questions-list h3 { font-size: 18px; color: #0050b3; border-bottom: 1px solid #eee; padding-bottom: 8px; }
    .added-questions-list ul { list-style: none; padding: 0; }
    .added-questions-list li { background-color: #f8f9fa; padding: 12px 15px; margin-bottom: 10px; border-radius: 4px; border: 1px solid #e9ecef; display: flex; justify-content: space-between; align-items: center; }
    .added-questions-list li .question-info { flex-grow: 1; display: flex; align-items: center; }
    .added-questions-list li .question-info .order-controls { margin-right: 15px; }
    .added-questions-list li .question-info .order-controls a { text-decoration: none; color: #007bff; margin: 0 3px; font-size: 1.2em; }
    .added-questions-list li .actions a { margin-left: 10px; text-decoration: none; }
    .actions .edit-link { color: #ffc107; } .actions .delete-link { color: #dc3545; }
    .message { padding: 10px; margin-bottom: 15px; border-radius: 4px; font-size: 14px; }
    .error-message { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;}
    .success-message { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb;}
</style>

<div class="form-container">
    <h1>编辑问卷 (ID: <?php echo htmlspecialchars($questionnaire_id); ?>)</h1>
    <?php if (!empty($error_message) && (strpos($error_message, '标题') !== false || strpos($error_message, '问卷信息') !== false)): ?> <div class="message error-message"><?php echo $error_message; ?></div> <?php endif; ?>
    <?php if (!empty($success_message) && strpos($success_message, '标题') !== false): ?> <div class="message success-message"><?php echo $success_message; ?></div> <?php endif; ?>

    <form action="dashboard.php?page=edit_questionnaire&id=<?php echo htmlspecialchars($questionnaire_id); ?>" method="POST">
        <div class="form-section">
            <label for="questionnaire_title_edit">问卷标题:</label>
            <input type="text" id="questionnaire_title_edit" name="questionnaire_title_edit" value="<?php echo htmlspecialchars($questionnaire_title); ?>" required>
        </div>
        <div class="form-section">
            <label for="submission_limit_edit">回收份数限制:</label>
            <input type="number" id="submission_limit_edit" name="submission_limit_edit" min="1" placeholder="留空表示无限制" value="<?php echo $questionnaire_submission_limit !== null ? $questionnaire_submission_limit : ''; ?>">
            <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">设置问卷最多可以回收多少份，留空表示无限制</small>
        </div>
        <button type="submit" name="update_title_action" class="btn-action">更新问卷信息</button>
    </form>
</div>
<hr style="margin: 30px auto; max-width: 800px;">

<div class="question-editor-container" style="max-width: 800px; margin: 20px auto;">
    <div class="question-types-sidebar">
        <h3><?php echo $editing_question_index !== null ? '编辑题型' : '添加题型'; ?></h3>
        <ul>
            <li><a href="dashboard.php?page=edit_questionnaire&id=<?php echo $questionnaire_id; ?>&type=name<?php echo $editing_question_index !== null ? '&action=edit_existing_question&q_idx='.$editing_question_index : ''; ?>" class="<?php echo $current_selected_type_for_form == 'name' ? 'active' : ''; ?>">姓名</a></li>
            <li><a href="dashboard.php?page=edit_questionnaire&id=<?php echo $questionnaire_id; ?>&type=id_card<?php echo $editing_question_index !== null ? '&action=edit_existing_question&q_idx='.$editing_question_index : ''; ?>" class="<?php echo $current_selected_type_for_form == 'id_card' ? 'active' : ''; ?>">身份证</a></li>
            <li><a href="dashboard.php?page=edit_questionnaire&id=<?php echo $questionnaire_id; ?>&type=phone<?php echo $editing_question_index !== null ? '&action=edit_existing_question&q_idx='.$editing_question_index : ''; ?>" class="<?php echo $current_selected_type_for_form == 'phone' ? 'active' : ''; ?>">手机</a></li>
            <li><a href="dashboard.php?page=edit_questionnaire&id=<?php echo $questionnaire_id; ?>&type=single_choice<?php echo $editing_question_index !== null ? '&action=edit_existing_question&q_idx='.$editing_question_index : ''; ?>" class="<?php echo $current_selected_type_for_form == 'single_choice' ? 'active' : ''; ?>">单选题</a></li>
            <li><a href="dashboard.php?page=edit_questionnaire&id=<?php echo $questionnaire_id; ?>&type=fill_blank<?php echo $editing_question_index !== null ? '&action=edit_existing_question&q_idx='.$editing_question_index : ''; ?>" class="<?php echo $current_selected_type_for_form == 'fill_blank' ? 'active' : ''; ?>">填空题</a></li>
        </ul>
         <?php if ($editing_question_index !== null): ?>
            <a href="dashboard.php?page=edit_questionnaire&id=<?php echo $questionnaire_id; ?>" style="display:block; text-align:center; margin-top:10px;">取消编辑 / 添加新题</a>
        <?php endif; ?>
    </div>

    <div class="question-settings-main">
        <h2><?php echo $editing_question_index !== null ? '修改问题' : '添加新问题'; ?></h2>
        <div class="questionnaire-title-display">问卷: <?php echo htmlspecialchars($questionnaire_title); ?></div>

        <?php if (!empty($error_message) && strpos($error_message, '标题') === false && strpos($error_message, '问卷信息') === false): ?> <div class="message error-message"><?php echo $error_message; ?></div> <?php endif; ?>
        <?php if (!empty($success_message) && strpos($success_message, '标题') === false && !isset($_GET['question_updated'])): ?> <div class="message success-message"><?php echo $success_message; ?></div> <?php endif; ?>
        <?php if (isset($_GET['question_updated'])): ?><div class="message success-message">问题已更新。请在下方列表中查看，或继续编辑/添加。</div><?php endif; ?>

        <?php if (!empty($current_selected_type_for_form)): ?>
        <form action="dashboard.php?page=edit_questionnaire&id=<?php echo $questionnaire_id; ?><?php echo $editing_question_index !== null ? '&action=edit_existing_question&q_idx='.$editing_question_index : ''; ?>" method="post">
            <input type="hidden" name="question_type_edit" value="<?php echo $current_selected_type_for_form; ?>">
            <?php if ($editing_question_index !== null): ?>
                <input type="hidden" name="editing_question_idx" value="<?php echo $editing_question_index; ?>">
            <?php endif; ?>
            
            <?php
            $label_placeholder_edit = '请输入题目内容';
            if ($editing_question_data) { 
                // $form_label_value and $form_options_value already set
            } else { 
                switch ($current_selected_type_for_form) {
                    case 'name': $form_label_value = '姓名'; $label_placeholder_edit = '例如：您的姓名'; break;
                    case 'id_card': $form_label_value = '身份证号码'; $label_placeholder_edit = '例如：您的身份证号'; break;
                    case 'phone': $form_label_value = '手机'; $label_placeholder_edit = '例如：您的手机号码'; break;
                }
            }
            ?>
            <div class="form-section">
                <label for="question_label_edit">题目/提示文字:</label>
                <input type="text" id="question_label_edit" name="question_label_edit" placeholder="<?php echo $label_placeholder_edit; ?>" value="<?php echo htmlspecialchars($form_label_value); ?>" required>
            </div>

            <div class="form-section" id="answer_limit_section" style="<?php echo in_array($current_selected_type_for_form, ['id_card', 'phone']) ? 'display: none;' : ''; ?>">
                <label for="question_answer_limit_edit">相同答案数量限制:</label>
                <input type="number" id="question_answer_limit_edit" name="question_answer_limit_edit" min="1" placeholder="留空表示无限制" value="<?php echo isset($form_answer_limit_value) && $form_answer_limit_value !== null ? $form_answer_limit_value : ''; ?>">
                <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">设置相同答案最多可以出现多少次，例如设置为3，则同一个姓名最多只能提交3次</small>
            </div>

            <?php if ($current_selected_type_for_form == 'single_choice'): ?>
            <div class="form-section options-container">
                <label>选项内容和限制:</label>
                <div id="options-wrapper-edit">
                    <?php
                    $options_to_display = $form_options_value;
                    if (empty($options_to_display)) {
                        $options_to_display = [
                            ['text' => '', 'limit' => null],
                            ['text' => '', 'limit' => null]
                        ];
                    }
                    // 兼容旧格式（字符串数组）
                    foreach ($options_to_display as $idx => $opt_val):
                        if (is_string($opt_val)) {
                            $opt_text = $opt_val;
                            $opt_limit = null;
                        } else {
                            $opt_text = $opt_val['text'] ?? '';
                            $opt_limit = $opt_val['limit'] ?? null;
                        }
                    ?>
                    <div class="option-item">
                        <input type="text" name="options_text_edit[]" placeholder="选项 <?php echo $idx + 1; ?>" value="<?php echo htmlspecialchars($opt_text); ?>" required style="flex: 2; margin-right: 8px;">
                        <input type="number" name="options_limit_edit[]" placeholder="限制数量" value="<?php echo $opt_limit !== null ? $opt_limit : ''; ?>" min="1" style="flex: 1; margin-right: 8px;">
                        <button type="button" class="btn-remove-option" onclick="removeOptionEdit(this)" <?php if(count($options_to_display) <=2 && $editing_question_index !== null) echo 'style="display:none;"'; elseif(count($options_to_display) <=2 && $editing_question_index === null && $idx<2) echo 'style="display:none;"';?> >删除</button>
                    </div>
                    <?php endforeach; ?>
                </div>
                <button type="button" class="btn-add-option" onclick="addOptionEdit()">添加选项</button>
                <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">限制数量：设置该选项最多可以被选择多少次，留空表示无限制</small>
            </div>
            <?php endif; ?>
            
            <button type="submit" name="<?php echo $editing_question_index !== null ? 'update_question_edit' : 'add_question_edit'; ?>" class="btn-action">
                <?php echo $editing_question_index !== null ? '更新此问题' : '添加此问题到列表'; ?>
            </button>
        </form>
        <?php elseif ($editing_question_index === null) : ?>
            <p>请从左侧选择一个题型以添加新问题，或从下方列表选择一个现有问题进行编辑。</p>
        <?php endif; ?>

        <div class="added-questions-list">
            <h3>当前问卷中的问题 (<?php echo count($current_questions); ?>)</h3>
            <?php if (!empty($current_questions)): ?>
            <ul>
                <?php

                ?>
                <?php foreach ($current_questions as $index => $q): ?>
                <li>
                    <div class="question-info">
                        <div class="order-controls">
                            <?php if ($index > 0): ?>
                                <a href="dashboard.php?page=edit_questionnaire&id=<?php echo $questionnaire_id; ?>&action=move_question_up&q_idx=<?php echo $index; ?>" title="上移">⬆️</a>
                            <?php else: echo "<span style='visibility:hidden;'>⬆️</span>"; endif; ?>
                            <?php if ($index < count($current_questions) - 1): ?>
                                <a href="dashboard.php?page=edit_questionnaire&id=<?php echo $questionnaire_id; ?>&action=move_question_down&q_idx=<?php echo $index; ?>" title="下移">⬇️</a>
                            <?php else: echo "<span style='visibility:hidden;'>⬇️</span>"; endif; ?>
                        </div>
                        <span><?php echo ($index + 1) . '. ' . htmlspecialchars($q['label']); ?></span>
                        <span style="font-size:0.8em; background-color:#6c757d; color:white; padding:2px 5px; border-radius:3px; margin-left: 8px;">
                            <?php
                            switch ($q['type']) {
                                case 'name': echo '姓名'; break; case 'id_card': echo '身份证'; break;
                                case 'phone': echo '手机'; break; case 'single_choice': echo '单选题'; break;
                                case 'fill_blank': echo '填空题'; break; default: echo htmlspecialchars($q['type']); break;
                            } ?>
                        </span>
                        <?php if (isset($q['answer_limit']) && $q['answer_limit'] !== null): ?>
                        <span style="font-size:0.8em; background-color:#17a2b8; color:white; padding:2px 5px; border-radius:3px; margin-left: 5px;">
                            答案限制: <?php echo $q['answer_limit']; ?>次
                        </span>
                        <?php endif; ?>

                        <?php
                        // 显示答案分布统计（仅对已保存的题目，排除身份证和手机号码）
                        if (isset($q['id']) && strpos($q['id'], 'new_') !== 0 && isset($q['answer_limit']) && $q['answer_limit'] !== null && !in_array($q['type'], ['id_card', 'phone'])) {
                            $stmt_answers = $mysqli->prepare("SELECT answer_value, COUNT(*) as count FROM submission_answers WHERE question_id = ? GROUP BY answer_value ORDER BY count DESC LIMIT 5");
                            if ($stmt_answers) {
                                $stmt_answers->bind_param("s", $q['id']);
                                $stmt_answers->execute();
                                $answers_result = $stmt_answers->get_result();
                                $answer_stats = [];
                                while ($answer_row = $answers_result->fetch_assoc()) {
                                    $answer_stats[] = $answer_row;
                                }
                                $stmt_answers->close();

                                if (!empty($answer_stats)) { ?>
                        <div style="margin-top: 8px; font-size: 0.8em; color: #555;">
                            <strong>答案分布（前5个）:</strong>
                            <ul style="margin: 5px 0 0 20px; padding: 0;">
                                <?php foreach ($answer_stats as $stat) {
                                    $percentage = $q['answer_limit'] > 0 ? ($stat['count'] / $q['answer_limit']) * 100 : 0;
                                    $color = $percentage >= 100 ? '#dc3545' : ($percentage >= 80 ? '#ffc107' : '#28a745');
                                ?>
                                <li style="margin-bottom: 2px;">
                                    <span style="color: <?php echo $color; ?>; font-weight: bold;">
                                        <?php echo htmlspecialchars($stat['answer_value']); ?>: <?php echo $stat['count']; ?>/<?php echo $q['answer_limit']; ?>
                                        <?php if ($percentage >= 100) { ?>
                                            (已满)
                                        <?php } elseif ($percentage >= 80) { ?>
                                            (接近满额)
                                        <?php } ?>
                                    </span>
                                </li>
                                <?php } ?>
                            </ul>
                        </div>
                        <?php } // end if (!empty($answer_stats))
                            } // end if ($stmt_answers)
                        } // end if (isset($q['id']) && ...)
                        ?>

                        <?php if ($q['type'] == 'single_choice' && !empty($q['options'])): ?>
                            <ul style="font-size:0.9em; color:#555; margin-left:20px; list-style-type: disc;">
                            <?php foreach($q['options'] as $opt):
                                if (is_string($opt)) {
                                    // 兼容旧格式
                                    $opt_text = $opt;
                                    $opt_limit = null;
                                } else {
                                    // 新格式
                                    $opt_text = $opt['text'] ?? '';
                                    $opt_limit = $opt['limit'] ?? null;
                                }

                                // 获取当前选项的选择数量
                                $option_count = 0;
                                if (isset($q['id']) && strpos($q['id'], 'new_') !== 0) {
                                    $stmt_opt_count = $mysqli->prepare("SELECT COUNT(*) as count FROM submission_answers WHERE question_id = ? AND answer_value = ?");
                                    if ($stmt_opt_count) {
                                        $stmt_opt_count->bind_param("ss", $q['id'], $opt_text);
                                        $stmt_opt_count->execute();
                                        $opt_count_result = $stmt_opt_count->get_result();
                                        $opt_count_data = $opt_count_result->fetch_assoc();
                                        $option_count = $opt_count_data['count'];
                                        $stmt_opt_count->close();
                                    }
                                }

                                echo '<li>' . htmlspecialchars($opt_text);

                                // 显示选择统计
                                if ($opt_limit !== null) {
                                    $percentage = $opt_limit > 0 ? ($option_count / $opt_limit) * 100 : 0;
                                    $color = $percentage >= 100 ? '#dc3545' : ($percentage >= 80 ? '#ffc107' : '#28a745');
                                    echo ' <span style="color: ' . $color . '; font-size: 0.8em; font-weight: bold;">(' . $option_count . '/' . $opt_limit;
                                    if ($percentage >= 100) {
                                        echo ' 已满';
                                    } elseif ($percentage >= 80) {
                                        echo ' 接近满额';
                                    }
                                    echo ')</span>';
                                } else {
                                    echo ' <span style="color: #6c757d; font-size: 0.8em;">(' . $option_count . '/无限制)</span>';
                                }
                                echo '</li>';
                            endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                    <div class="actions">
                        <a href="dashboard.php?page=edit_questionnaire&id=<?php echo $questionnaire_id; ?>&action=edit_existing_question&q_idx=<?php echo $index; ?>" class="edit-link">编辑</a>
                        <a href="dashboard.php?page=edit_questionnaire&id=<?php echo $questionnaire_id; ?>&action=delete_question_edit&q_idx=<?php echo $index; ?>" class="delete-link" onclick="return confirm('确定要从列表中删除这个问题吗？');">删除</a>
                    </div>
                </li>
                <?php endforeach; ?>
            </ul>
            <?php else: ?>
            <p>此问卷暂无问题。</p>
            <?php endif; ?>
            
            <form action="dashboard.php?page=edit_questionnaire&id=<?php echo $questionnaire_id; ?>" method="post" style="margin-top:30px;">
                <button type="submit" name="save_questionnaire_edit" class="btn-save-all btn-action">保存</button>
            </form>
            <div style="clear:both;"></div>
        </div>
    </div>
</div>
<p style="text-align: center; max-width: 800px; margin: 30px auto;">
    <a href="dashboard.php?page=all_questionnaires" class="btn-secondary-custom">放弃返回</a>
</p>

<script>
function addOptionEdit() {
    const wrapper = document.getElementById('options-wrapper-edit');
    const optionItems = wrapper.getElementsByClassName('option-item');
    const newOption = document.createElement('div');
    newOption.className = 'option-item';
    newOption.innerHTML = `<input type="text" name="options_text_edit[]" placeholder="选项 ${optionItems.length + 1}" required style="flex: 2; margin-right: 8px;"><input type="number" name="options_limit_edit[]" placeholder="限制数量" min="1" style="flex: 1; margin-right: 8px;"><button type="button" class="btn-remove-option" onclick="removeOptionEdit(this)">删除</button>`;
    wrapper.appendChild(newOption);
    updateRemoveButtonsEdit();
}
function removeOptionEdit(button) {
    button.parentNode.remove();
    updateRemoveButtonsEdit();
    const wrapper = document.getElementById('options-wrapper-edit');
    const optionInputs = wrapper.querySelectorAll('.option-item input[type="text"]');
    optionInputs.forEach((input, index) => { input.placeholder = `选项 ${index + 1}`; });
}
function updateRemoveButtonsEdit() {
    const wrapper = document.getElementById('options-wrapper-edit');
    if (!wrapper) return;
    const removeButtons = wrapper.getElementsByClassName('btn-remove-option');
    const optionItemsCount = wrapper.getElementsByClassName('option-item').length;
    const display = optionItemsCount <= 2 ? 'none' : 'inline-block';
    for (let btn of removeButtons) { btn.style.display = display; }
}
document.addEventListener('DOMContentLoaded', function() { 
    if (document.getElementById('options-wrapper-edit')) {
        updateRemoveButtonsEdit(); 
    }
});
</script>
