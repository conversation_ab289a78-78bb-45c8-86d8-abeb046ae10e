<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Ensure DB connection is available
if (!isset($mysqli) || !$mysqli) {
    include_once '../config/db_config.php';
    if (!isset($mysqli) || !$mysqli) {
        exit("Database connection failed for export.");
    }
}

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    exit("Unauthorized access.");
}

$questionnaire_id = $_GET['id'] ?? null;
$questionnaire_title_orig = 'export'; // Default filename part
$error_message = '';
$question_headers_for_export = []; 
$submissions_for_export = [];    

if (empty($questionnaire_id)) {
    exit("Error: Questionnaire ID not provided for export.");
}

// 1. Get questionnaire title for filename
$stmt_q_title = $mysqli->prepare("SELECT title FROM questionnaires WHERE id = ?");
if ($stmt_q_title) {
    $stmt_q_title->bind_param("s", $questionnaire_id);
    $stmt_q_title->execute();
    $result_q_title = $stmt_q_title->get_result();
    if ($data_q_title = $result_q_title->fetch_assoc()) {
        $questionnaire_title_orig = $data_q_title['title'];
    } else {
        exit("Error: Questionnaire not found.");
    }
    $stmt_q_title->close();
} else {
    exit("Error preparing to fetch questionnaire title: " . $mysqli->error);
}

// 2. Get all questions for this questionnaire
if (empty($error_message)) {
    $stmt_qs_headers = $mysqli->prepare("SELECT id, label FROM questions WHERE questionnaire_id = ? ORDER BY sort_order ASC");
    if ($stmt_qs_headers) {
        $stmt_qs_headers->bind_param("s", $questionnaire_id);
        $stmt_qs_headers->execute();
        $result_qs_headers = $stmt_qs_headers->get_result();
        while ($q_row = $result_qs_headers->fetch_assoc()) {
            $question_headers_for_export[] = ['id' => $q_row['id'], 'label' => $q_row['label']];
        }
        $stmt_qs_headers->close();
    } else {
        exit("Error fetching question headers: " . $mysqli->error);
    }
}

// 3. Get submissions and their answers
if (empty($error_message)) {
    $stmt_sub = $mysqli->prepare("SELECT id, submitted_at, ip_address FROM submissions WHERE questionnaire_id = ? ORDER BY submitted_at ASC");
    if (!$stmt_sub) {
        exit("Error preparing to fetch submissions: " . $mysqli->error);
    }
    
    $stmt_sub->bind_param("s", $questionnaire_id);
    $stmt_sub->execute();
    $result_sub = $stmt_sub->get_result();
    
    while ($submission_row = $result_sub->fetch_assoc()) {
        $submission_id = $submission_row['id'];
        $current_submission_answers = [];

        $stmt_ans = $mysqli->prepare("SELECT question_id, answer_value FROM submission_answers WHERE submission_id = ?");
        if ($stmt_ans) {
            $stmt_ans->bind_param("i", $submission_id);
            $stmt_ans->execute();
            $result_ans = $stmt_ans->get_result();
            while ($answer_row = $result_ans->fetch_assoc()) {
                $current_submission_answers[$answer_row['question_id']] = $answer_row['answer_value'];
            }
            $stmt_ans->close();
        }
        
        $submissions_for_export[] = [
            'submitted_at' => $submission_row['submitted_at'],
            'ip_address' => $submission_row['ip_address'],
            'answers' => $current_submission_answers
        ];
    }
    $stmt_sub->close();
}

if (!empty($error_message)) {
    exit($error_message);
}

// --- Excel (HTML Table) Generation ---
$clean_title = preg_replace('/[^a-zA-Z0-9_ -]/s', '', $questionnaire_title_orig);
$clean_title = str_replace(' ', '_', $clean_title);
$filename = "submissions_" . $clean_title . "_" . date('YmdHis') . ".xls"; // Use .xls for HTML table recognized by Excel

header('Content-Type: application/vnd.ms-excel; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
echo '<head>';
echo '<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />';
echo '<!--[if gte mso 9]><xml>';
echo '<x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet>';
echo '<x:Name>Submissions</x:Name>';
echo '<x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>';
echo '</x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook>';
echo '</xml><![endif]-->';
echo '<style>td {mso-number-format:"\@";} br {mso-data-placement:same-cell;}</style>'; // Force text format for all cells, ensure newlines are preserved
echo '</head><body>';
echo '<table>';

// Header row
echo '<thead><tr>';
echo '<th>序号</th>';
foreach ($question_headers_for_export as $header) {
    echo '<th>' . htmlspecialchars($header['label']) . '</th>';
}
echo '<th>IP地址</th>';
echo '<th>提交时间</th>';
echo '</tr></thead>';

// Data rows
echo '<tbody>';
foreach ($submissions_for_export as $index => $sub) {
    echo '<tr>';
    echo '<td>' . ($index + 1) . '</td>'; // 序号

    foreach ($question_headers_for_export as $header) {
        $question_id = $header['id'];
        $answer_value = isset($sub['answers'][$question_id]) ? $sub['answers'][$question_id] : '';
        echo '<td>' . htmlspecialchars($answer_value) . '</td>';
    }

    echo '<td>' . htmlspecialchars($sub['ip_address']) . '</td>';
    echo '<td>' . htmlspecialchars(date('Y-m-d H:i:s', strtotime($sub['submitted_at']))) . '</td>';
    echo '</tr>';
}
echo '</tbody>';
echo '</table>';
echo '</body></html>';
exit;
?>
