<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Ensure DB connection is available
if (!isset($mysqli) || !$mysqli) {
    include_once '../config/db_config.php';
    if (!isset($mysqli) || !$mysqli) {
        // Fallback if db_config.php didn't establish $mysqli for some reason
        // This is a critical error, should not happen if db_config is correct
        $_SESSION['message'] = ['type' => 'error', 'text' => '数据库连接失败，请检查配置文件。'];
        // Attempt to redirect, though without DB, session messages might not persist if session setup also failed.
        // A more robust error page might be needed in a real-world scenario.
        header("Location: dashboard.php?page=all_questionnaires");
        exit("Database connection failed."); // Hard exit if redirect fails or isn't appropriate
    }
}


// 检查用户是否已登录，否则重定向到登录页面
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header("Location: login.php");
    exit;
}

// global $mysqli; // $mysqli is now ensured above

$action = $_GET['action'] ?? '';
$questionnaire_id = $_GET['id'] ?? '';

if (empty($action) || empty($questionnaire_id)) {
    $_SESSION['message'] = ['type' => 'error', 'text' => '无效的操作或问卷ID。'];
    header("Location: dashboard.php?page=all_questionnaires");
    exit;
}

if ($action == 'delete') {
    // 软删除：将状态更新为 'deleted'
    $stmt = $mysqli->prepare("UPDATE questionnaires SET status = 'deleted' WHERE id = ?");
    if ($stmt) {
        $stmt->bind_param("s", $questionnaire_id);
        if ($stmt->execute()) {
            $_SESSION['message'] = ['type' => 'success', 'text' => '问卷已成功移至回收站。'];
        } else {
            $_SESSION['message'] = ['type' => 'error', 'text' => '操作失败: ' . $stmt->error];
        }
        $stmt->close();
    } else {
        $_SESSION['message'] = ['type' => 'error', 'text' => '操作准备失败: ' . $mysqli->error];
    }
    header("Location: dashboard.php?page=all_questionnaires");
    exit;
} elseif ($action == 'restore') {
    // 从回收站恢复：将状态更新为 'draft'
    $stmt = $mysqli->prepare("UPDATE questionnaires SET status = 'draft' WHERE id = ? AND status = 'deleted'");
    if ($stmt) {
        $stmt->bind_param("s", $questionnaire_id);
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                $_SESSION['message'] = ['type' => 'success', 'text' => '问卷已成功从回收站恢复。'];
            } else {
                $_SESSION['message'] = ['type' => 'error', 'text' => '未找到问卷或问卷不在回收站中。'];
            }
        } else {
            $_SESSION['message'] = ['type' => 'error', 'text' => '恢复操作失败: ' . $stmt->error];
        }
        $stmt->close();
    } else {
        $_SESSION['message'] = ['type' => 'error', 'text' => '恢复操作准备失败: ' . $mysqli->error];
    }
    header("Location: dashboard.php?page=recycle_bin"); // 操作后返回回收站页面
    exit;
} elseif ($action == 'permanent_delete') {
    // 永久删除 (需要先删除关联的 questions 表中的记录，因为有外键约束)
    $mysqli->begin_transaction();
    try {
        // 1. 删除 questions 表中的相关问题
        $stmt_q = $mysqli->prepare("DELETE FROM questions WHERE questionnaire_id = ?");
        if (!$stmt_q) throw new Exception("Prepare questions delete failed: " . $mysqli->error);
        $stmt_q->bind_param("s", $questionnaire_id);
        if (!$stmt_q->execute()) throw new Exception("Execute questions delete failed: " . $stmt_q->error);
        $stmt_q->close();

        // 2. 删除 questionnaires 表中的问卷
        $stmt_qn = $mysqli->prepare("DELETE FROM questionnaires WHERE id = ? AND status = 'deleted'");
        if (!$stmt_qn) throw new Exception("Prepare questionnaire delete failed: " . $mysqli->error);
        $stmt_qn->bind_param("s", $questionnaire_id);
        if (!$stmt_qn->execute()) throw new Exception("Execute questionnaire delete failed: " . $stmt_qn->error);
        
        if ($stmt_qn->affected_rows > 0) {
            $_SESSION['message'] = ['type' => 'success', 'text' => '问卷已永久删除。'];
        } else {
            $_SESSION['message'] = ['type' => 'error', 'text' => '未找到问卷或问卷不在回收站中，无法永久删除。'];
        }
        $stmt_qn->close();

        $mysqli->commit();
    } catch (Exception $e) {
        $mysqli->rollback();
        $_SESSION['message'] = ['type' => 'error', 'text' => '永久删除失败: ' . $e->getMessage()];
    }
    header("Location: dashboard.php?page=recycle_bin"); // 操作后返回回收站页面
    exit;
} elseif ($action == 'publish') {
    // 发布问卷：将状态更新为 'published'
    $stmt = $mysqli->prepare("UPDATE questionnaires SET status = 'published' WHERE id = ? AND (status = 'draft' OR status = 'archived')");
    if ($stmt) {
        $stmt->bind_param("s", $questionnaire_id);
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                $_SESSION['message'] = ['type' => 'success', 'text' => '问卷已成功发布。'];
            } else {
                $_SESSION['message'] = ['type' => 'error', 'text' => '问卷可能已被发布或状态不正确。'];
            }
        } else {
            $_SESSION['message'] = ['type' => 'error', 'text' => '发布操作失败: ' . $stmt->error];
        }
        $stmt->close();
    } else {
        $_SESSION['message'] = ['type' => 'error', 'text' => '发布操作准备失败: ' . $mysqli->error];
    }
    header("Location: dashboard.php?page=all_questionnaires");
    exit;
} elseif ($action == 'unpublish') {
    // 取消发布问卷：将状态更新为 'draft'
    $stmt = $mysqli->prepare("UPDATE questionnaires SET status = 'draft' WHERE id = ? AND status = 'published'");
    if ($stmt) {
        $stmt->bind_param("s", $questionnaire_id);
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                $_SESSION['message'] = ['type' => 'success', 'text' => '问卷已取消发布，状态已设为草稿。'];
            } else {
                $_SESSION['message'] = ['type' => 'error', 'text' => '问卷可能未发布或状态不正确。'];
            }
        } else {
            $_SESSION['message'] = ['type' => 'error', 'text' => '取消发布操作失败: ' . $stmt->error];
        }
        $stmt->close();
    } else {
        $_SESSION['message'] = ['type' => 'error', 'text' => '取消发布操作准备失败: ' . $mysqli->error];
    }
    header("Location: dashboard.php?page=all_questionnaires");
    exit;
} else {
    $_SESSION['message'] = ['type' => 'error', 'text' => '未知的操作。'];
    header("Location: dashboard.php?page=all_questionnaires");
    exit;
}

// 通常这个文件不会直接显示任何HTML，它只处理逻辑并重定向
// 但为了符合 dashboard.php 的 include 结构，可以保留一个空的输出或者一个简单的提示
// echo "处理中...";
?>
