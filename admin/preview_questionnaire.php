<?php
// Ensure this file is included by dashboard.php and user is logged in
if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME']) || !isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME'])) {
        echo "此页面不能直接访问。";
    }
    exit;
}

global $mysqli; // $mysqli from dashboard.php

$questionnaire_id = $_GET['id'] ?? null;
$questionnaire_title = '';
$questionnaire_status = '';
$questions = [];
$error_message = '';

if (empty($questionnaire_id)) {
    $error_message = "错误：未提供问卷ID进行预览。";
} else {
    // 1. Get questionnaire title and status (admin can preview any existing questionnaire)
    $stmt_q = $mysqli->prepare("SELECT title, status FROM questionnaires WHERE id = ?");
    if (!$stmt_q) {
        $error_message = "数据库查询准备失败 (问卷信息): " . $mysqli->error;
    } else {
        $stmt_q->bind_param("s", $questionnaire_id);
        $stmt_q->execute();
        $result_q = $stmt_q->get_result();
        if ($result_q->num_rows > 0) {
            $questionnaire_data = $result_q->fetch_assoc();
            $questionnaire_title = $questionnaire_data['title'];
            $questionnaire_status = $questionnaire_data['status'];
        } else {
            $error_message = "未找到ID为 " . htmlspecialchars($questionnaire_id) . " 的问卷。";
        }
        $stmt_q->close();
    }

    // 2. If questionnaire found, get its questions
    if (empty($error_message)) {
        $stmt_qs = $mysqli->prepare("SELECT id, type, label, options FROM questions WHERE questionnaire_id = ? ORDER BY sort_order ASC");
        if (!$stmt_qs) {
            $error_message = "数据库查询准备失败 (问卷问题): " . $mysqli->error;
        } else {
            $stmt_qs->bind_param("s", $questionnaire_id);
            $stmt_qs->execute();
            $result_qs = $stmt_qs->get_result();
            while ($row = $result_qs->fetch_assoc()) {
                if (!empty($row['options'])) {
                    $row['options'] = json_decode($row['options'], true);
                }
                $questions[] = $row;
            }
            $stmt_qs->close();

            if (empty($questions) && empty($error_message)) {
                 // This is a preview, so it's okay if there are no questions yet.
            }
        }
    }
}

$status_map_preview = [
    'draft' => '草稿',
    'published' => '已发布',
    'archived' => '已归档',
    'deleted' => '已删除 (回收站)'
];
$display_status = isset($status_map_preview[$questionnaire_status]) ? $status_map_preview[$questionnaire_status] : htmlspecialchars($questionnaire_status);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预览: <?php echo !empty($questionnaire_title) ? htmlspecialchars($questionnaire_title) : '问卷'; ?> - 管理后台</title>
    <style>
        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 20px; /* Add padding to body for better spacing if content is directly in main-content */
        }
        .preview-container {
            width: 90%;
            max-width: 800px;
            margin: 20px auto; /* Center it if not already centered by main-content */
            background-color: #ffffff;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .preview-container header h1 {
            color: #0050b3;
            text-align: center;
            margin-bottom: 10px;
            font-size: 24px;
        }
        .preview-container header .status-badge {
            display: block;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1em;
            color: #555;
        }
        .status-badge span {
            background-color: #e6f7ff;
            color: #0050b3;
            padding: 5px 10px;
            border-radius: 4px;
            border: 1px solid #b3d9ff;
        }
        .question-item-preview {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #d9d9d9;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .question-item-preview .label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        .question-item-preview .input-area {
            padding: 10px;
            background-color: #fff;
            border: 1px dashed #ccc;
            border-radius: 4px;
            color: #777;
            min-height: 30px; /* Ensure some height for visual representation */
        }
        .question-item-preview select, .question-item-preview input[type="text"] {
             width: calc(100% - 22px);
             padding: 10px;
             border: 1px solid #ccc;
             border-radius: 4px;
             box-sizing: border-box;
             background-color: #f9f9f9; /* Indicate non-interactive */
        }
        .error-message-box {
            padding: 15px;
            margin: 20px auto;
            border-radius: 4px;
            background-color: #fff1f0;
            color: #cf1322;
            border: 1px solid #ffa39e;
            text-align: center;
        }
        .no-questions-message {
            text-align: center;
            padding: 20px;
            color: #555;
            background-color: #f9f9f9;
            border: 1px dashed #ddd;
            border-radius: 4px;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 8px 15px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .back-link:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <?php if (!empty($error_message)): ?>
            <div class="error-message-box"><?php echo $error_message; ?></div>
        <?php else: ?>
            <header>
                <h1><?php echo htmlspecialchars($questionnaire_title); ?></h1>
                <div class="status-badge">当前状态: <span><?php echo htmlspecialchars($display_status); ?></span></div>
            </header>

            <?php if (!empty($questions)): ?>
                <?php foreach ($questions as $question): ?>
                    <div class="question-item-preview">
                        <span class="label"><?php echo htmlspecialchars($question['label']); ?> (类型: <?php echo htmlspecialchars($question['type']); ?>)</span>
                        <div class="input-area">
                            <?php if ($question['type'] == 'name' || $question['type'] == 'id_card' || $question['type'] == 'phone' || $question['type'] == 'fill_blank'): ?>
                                <input type="text" placeholder="用户将在此处填写" disabled readonly style="width: 95%;">
                            <?php elseif ($question['type'] == 'single_choice' && !empty($question['options'])): ?>
                                <select disabled readonly style="width: 100%;">
                                    <option value="">用户将在此处选择...</option>
                                    <?php foreach ($question['options'] as $option_value): ?>
                                        <option value="<?php echo htmlspecialchars($option_value); ?>">
                                            <?php echo htmlspecialchars($option_value); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            <?php else: ?>
                                预览占位符 (未知或无选项的题型)
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <p class="no-questions-message">此问卷当前没有题目可供预览。</p>
            <?php endif; ?>
        <?php endif; ?>
        <p style="text-align: center; margin-top: 30px;">
            <a href="dashboard.php?page=all_questionnaires" class="back-link">返回问卷列表</a>
        </p>
    </div>
</body>
</html>
