<?php
// 确保此文件被 dashboard.php 正确包含
if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME'])) {
    // 如果直接访问，则重定向或显示错误
    // header('Location: dashboard.php');
    // exit;
    echo "此页面不能直接访问。";
    exit;
}

// 数据库连接已在 dashboard.php 中包含 (via db_config.php)
// global $mysqli; // 已经在 dashboard.php 中包含了 db_config.php, $mysqli 可用
global $mysqli;

// 获取问卷列表（包含回收份数限制和当前回收数量）
$questionnaires = [];
$sql = "SELECT q.id, q.title, q.created_at, q.status, q.submission_limit,
               COUNT(s.id) as submission_count
        FROM questionnaires q
        LEFT JOIN submissions s ON q.id = s.questionnaire_id
        WHERE q.status != 'deleted'
        GROUP BY q.id, q.title, q.created_at, q.status, q.submission_limit
        ORDER BY q.created_at DESC";
$result = $mysqli->query($sql);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $questionnaires[] = $row;
    }
    $result->free();
} else {
    echo "<p style='color:red;'>获取问卷列表失败: " . $mysqli->error . "</p>";
}

$status_map = [
    'draft' => '草稿',
    'published' => '已发布',
    'archived' => '已归档'
];

?>
<style>
    .questionnaire-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .questionnaire-table th, .questionnaire-table td {
        border: 1px solid #ddd;
        padding: 10px 12px;
        text-align: left;
    }
    .questionnaire-table th {
        background-color: #e6f7ff; /* 淡蓝色表头 */
        color: #0050b3; /* 深蓝色文字 */
        font-weight: bold;
    }
    .questionnaire-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }
    .questionnaire-table tr:hover {
        background-color: #f1f1f1;
    }
    .questionnaire-table td a {
        color: #007bff;
        text-decoration: none;
        margin-right: 8px;
    }
    .questionnaire-table td a:hover {
        text-decoration: underline;
    }
    .no-questionnaires {
        padding: 15px;
        background-color: #fff3cd;
        border: 1px solid #ffeeba;
        color: #856404;
        border-radius: 4px;
        text-align: center;
    }
    .action-links a.delete {
        color: #dc3545;
    }
</style>

<h1>问卷列表</h1>

<?php
// 显示会话消息 (例如删除成功/失败)
if (isset($_SESSION['message'])) {
    $msg_type = $_SESSION['message']['type'] == 'success' ? 'success-message' : 'error-message';
    echo "<div class='message {$msg_type}'>" . htmlspecialchars($_SESSION['message']['text']) . "</div>";
    unset($_SESSION['message']);
}
?>

<?php if (!empty($questionnaires)): ?>
<table class="questionnaire-table">
    <thead>
        <tr>
            <th>ID</th>
            <th>问卷标题</th>
            <th>创建时间</th>
            <th>状态</th>
            <th>回收情况</th>
            <th>操作</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($questionnaires as $q): ?>
        <tr>
            <td><a href="../index.php?id=<?php echo htmlspecialchars($q['id']); ?>" target="_blank"><?php echo htmlspecialchars($q['id']); ?></a></td>
            <td><a href="../index.php?id=<?php echo htmlspecialchars($q['id']); ?>" target="_blank"><?php echo htmlspecialchars($q['title']); ?></a></td>
            <td><?php echo date('Y-m-d H:i', strtotime($q['created_at'])); ?></td>
            <td><?php echo isset($status_map[$q['status']]) ? $status_map[$q['status']] : htmlspecialchars($q['status']); ?></td>
            <td>
                <?php
                $current_count = (int)$q['submission_count'];
                $limit = $q['submission_limit'];
                if ($limit !== null) {
                    $limit = (int)$limit;
                    $percentage = $limit > 0 ? ($current_count / $limit) * 100 : 0;
                    $color = $percentage >= 100 ? '#dc3545' : ($percentage >= 80 ? '#ffc107' : '#28a745');
                    echo "<span style='color: {$color}; font-weight: bold;'>{$current_count}/{$limit}</span>";
                    if ($percentage >= 100) {
                        echo " <span style='color: #dc3545; font-size: 12px;'>(已满)</span>";
                    } elseif ($percentage >= 80) {
                        echo " <span style='color: #ffc107; font-size: 12px;'>(接近满额)</span>";
                    }
                } else {
                    echo "<span style='color: #6c757d;'>{$current_count}/无限制</span>";
                }
                ?>
            </td>
            <td class="action-links">
                <a href="dashboard.php?page=edit_questionnaire&id=<?php echo $q['id']; ?>">编辑</a>
                <a href="dashboard.php?page=preview_questionnaire&id=<?php echo $q['id']; ?>" target="_blank">预览</a>
                <?php if ($q['status'] == 'draft'): ?>
                    <a href="manage_questionnaire.php?action=publish&id=<?php echo $q['id']; ?>" style="color: green;" onclick="return confirm('确定要发布此问卷吗？发布后用户将可以访问。');">发布</a>
                <?php elseif ($q['status'] == 'published'): ?>
                    <a href="manage_questionnaire.php?action=unpublish&id=<?php echo $q['id']; ?>" style="color: orange;" onclick="return confirm('确定要取消发布此问卷吗？取消后用户将无法访问。');">取消发布</a>
                <?php endif; ?>
                <a href="dashboard.php?page=view_submissions&id=<?php echo $q['id']; ?>" style="color: purple;">查看答卷</a>
                <a href="manage_questionnaire.php?action=delete&id=<?php echo $q['id']; ?>" class="delete" onclick="return confirm('确定要将此问卷移至回收站吗？');">删除</a>
            </td>
        </tr>
        <?php endforeach; ?>
    </tbody>
</table>
<?php else: ?>
<p class="no-questionnaires">目前还没有任何问卷。 <a href="dashboard.php?page=create_questionnaire_title">立即创建新问卷</a></p>
<?php endif; ?>
