<?php
// 确保此文件被 dashboard.php 正确包含
if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME'])) {
    echo "此页面不能直接访问。";
    exit;
}

global $mysqli;

// 获取回收站中的问卷列表
$deleted_questionnaires = [];
$sql = "SELECT id, title, created_at FROM questionnaires WHERE status = 'deleted' ORDER BY created_at DESC";
$result = $mysqli->query($sql);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $deleted_questionnaires[] = $row;
    }
    $result->free();
} else {
    echo "<p style='color:red;'>获取回收站问卷列表失败: " . $mysqli->error . "</p>";
}
?>
<style>
    /* 使用与 questionnaire_list.php 类似的样式 */
    .questionnaire-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .questionnaire-table th, .questionnaire-table td {
        border: 1px solid #ddd;
        padding: 10px 12px;
        text-align: left;
    }
    .questionnaire-table th {
        background-color: #e6f7ff; /* 淡蓝色表头 */
        color: #0050b3; /* 深蓝色文字 */
        font-weight: bold;
    }
    .questionnaire-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }
    .questionnaire-table tr:hover {
        background-color: #f1f1f1;
    }
    .questionnaire-table td a {
        color: #007bff;
        text-decoration: none;
        margin-right: 8px;
    }
    .questionnaire-table td a:hover {
        text-decoration: underline;
    }
    .no-questionnaires { /* 可以复用 */
        padding: 15px;
        background-color: #fff3cd;
        border: 1px solid #ffeeba;
        color: #856404;
        border-radius: 4px;
        text-align: center;
    }
    .action-links a.restore {
        color: #28a745; /* 绿色恢复按钮 */
    }
    .action-links a.permanent-delete {
        color: #dc3545; /* 红色永久删除按钮 */
    }
    .message { padding: 10px; margin-bottom: 15px; border-radius: 4px; font-size: 14px; }
    .error-message { background-color: #ffebee; color: #c62828; border: 1px solid #ef9a9a; }
    .success-message { background-color: #e8f5e9; color: #2e7d32; border: 1px solid #a5d6a7; }
</style>

<h1>回收站</h1>

<?php
// 显示会话消息 (例如恢复/删除成功/失败)
if (isset($_SESSION['message'])) {
    $msg_type = $_SESSION['message']['type'] == 'success' ? 'success-message' : 'error-message';
    echo "<div class='message {$msg_type}'>" . htmlspecialchars($_SESSION['message']['text']) . "</div>";
    unset($_SESSION['message']);
}
?>

<?php if (!empty($deleted_questionnaires)): ?>
<table class="questionnaire-table">
    <thead>
        <tr>
            <th>ID</th>
            <th>问卷标题</th>
            <th>删除时间 (创建时间)</th>
            <th>操作</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($deleted_questionnaires as $q): ?>
        <tr>
            <td><?php echo htmlspecialchars($q['id']); ?></td>
            <td><?php echo htmlspecialchars($q['title']); ?></td>
            <td><?php echo date('Y-m-d H:i', strtotime($q['created_at'])); ?></td> <!-- 这里显示的是创建时间，因为我们没有单独的删除时间字段 -->
            <td class="action-links">
                <a href="dashboard.php?page=manage_questionnaire&action=restore&id=<?php echo $q['id']; ?>" class="restore" onclick="return confirm('确定要恢复此问卷吗？');">恢复</a>
                <a href="dashboard.php?page=manage_questionnaire&action=permanent_delete&id=<?php echo $q['id']; ?>" class="permanent-delete" onclick="return confirm('确定要永久删除此问卷吗？此操作不可撤销！');">永久删除</a>
            </td>
        </tr>
        <?php endforeach; ?>
    </tbody>
</table>
<?php else: ?>
<p class="no-questionnaires">回收站是空的。</p>
<?php endif; ?>
