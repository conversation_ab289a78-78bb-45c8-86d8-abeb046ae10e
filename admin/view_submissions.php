<?php
// Ensure this file is included by dashboard.php and user is logged in
if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME']) || !isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME'])) {
        echo "此页面不能直接访问。";
    }
    exit;
}

global $mysqli; // $mysqli from dashboard.php

$questionnaire_id = $_GET['id'] ?? null;
$questionnaire_title = '';
$error_message = '';
$question_headers = []; // To store ['id' => question_id, 'label' => question_label]
$processed_submissions = []; // To store submissions formatted for table display

if (empty($questionnaire_id)) {
    $error_message = "错误：未提供问卷ID以查看答卷。";
} else {
    // 1. Get questionnaire title
    $stmt_q_title = $mysqli->prepare("SELECT title FROM questionnaires WHERE id = ?");
    if (!$stmt_q_title) {
        $error_message = "数据库查询准备失败 (问卷标题): " . $mysqli->error;
    } else {
        $stmt_q_title->bind_param("s", $questionnaire_id);
        $stmt_q_title->execute();
        $result_q_title = $stmt_q_title->get_result();
        if ($data_q_title = $result_q_title->fetch_assoc()) {
            $questionnaire_title = $data_q_title['title'];
        } else {
            $error_message = "未找到ID为 " . htmlspecialchars($questionnaire_id) . " 的问卷。";
        }
        $stmt_q_title->close();
    }

    // 2. Get all questions for this questionnaire to serve as table headers
    // and to map answers correctly. Order them by sort_order.
    if (empty($error_message)) {
        $stmt_qs_headers = $mysqli->prepare("SELECT id, label FROM questions WHERE questionnaire_id = ? ORDER BY sort_order ASC");
        if ($stmt_qs_headers) {
            $stmt_qs_headers->bind_param("s", $questionnaire_id);
            $stmt_qs_headers->execute();
            $result_qs_headers = $stmt_qs_headers->get_result();
            while ($q_row = $result_qs_headers->fetch_assoc()) {
                $question_headers[] = ['id' => $q_row['id'], 'label' => $q_row['label']];
            }
            $stmt_qs_headers->close();
            if (empty($question_headers) && empty($error_message)) {
                // It's possible a questionnaire has no questions, though unusual.
                // $error_message = "此问卷没有定义任何问题。"; // Or handle gracefully
            }
        } else {
            $error_message = "获取问卷问题列表失败: " . $mysqli->error;
        }
    }

    // 3. Get submissions and their answers, structured for table display
    if (empty($error_message)) {
        $stmt_sub = $mysqli->prepare("SELECT id, submitted_at, ip_address FROM submissions WHERE questionnaire_id = ? ORDER BY submitted_at DESC");
        if (!$stmt_sub) {
            $error_message = "数据库查询准备失败 (答卷列表): " . $mysqli->error;
        } else {
            $stmt_sub->bind_param("s", $questionnaire_id);
            $stmt_sub->execute();
            $result_sub = $stmt_sub->get_result();
            while ($submission_row = $result_sub->fetch_assoc()) {
                $submission_id = $submission_row['id'];
                $current_submission_answers = []; // Keyed by question_id

                // Fetch answers for this submission
                $stmt_ans = $mysqli->prepare("SELECT question_id, answer_value FROM submission_answers WHERE submission_id = ?");
                if ($stmt_ans) {
                    $stmt_ans->bind_param("i", $submission_id);
                    $stmt_ans->execute();
                    $result_ans = $stmt_ans->get_result();
                    while ($answer_row = $result_ans->fetch_assoc()) {
                        $current_submission_answers[$answer_row['question_id']] = $answer_row['answer_value'];
                    }
                    $stmt_ans->close();
                } else {
                    // Log error or handle, e.g., by adding an error message to the submission's display
                    // For simplicity, we'll just have missing answers if this fails.
                }
                
                $processed_submissions[] = [
                    'submitted_at' => $submission_row['submitted_at'],
                    'ip_address' => $submission_row['ip_address'],
                    'answers' => $current_submission_answers // This is now an associative array: question_id => answer_value
                ];
            }
            $stmt_sub->close();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查看答卷: <?php echo htmlspecialchars($questionnaire_title); ?> - 管理后台</title>
    <style>
        body { font-family: 'Arial', sans-serif; background-color: #f0f2f5; color: #333; line-height: 1.6; margin: 0; padding: 20px; }
        .submissions-container { width: 95%; max-width: 1200px; margin: 20px auto; background-color: #fff; padding: 20px 30px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); overflow-x: auto; }
        .submissions-container h1 { color: #0050b3; text-align: center; margin-bottom: 10px; font-size: 24px; }
        .submissions-container h2 { color: #003a8c; margin-top: 20px; margin-bottom: 15px; font-size: 20px; }
        
        .submissions-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .submissions-table th, .submissions-table td { border: 1px solid #ddd; padding: 10px 12px; text-align: left; vertical-align: top; }
        .submissions-table th { background-color: #e6f7ff; color: #0050b3; font-weight: bold; white-space: nowrap; }
        .submissions-table tr:nth-child(even) { background-color: #f9f9f9; }
        .submissions-table tr:hover { background-color: #f1f1f1; }
        .submissions-table td { white-space: pre-wrap; word-wrap: break-word; }

        .error-message-box, .no-submissions-message { text-align: center; padding: 15px; margin: 20px auto; border-radius: 4px; }
        .error-message-box { background-color: #fff1f0; color: #cf1322; border: 1px solid #ffa39e; }
        .no-submissions-message { background-color: #f6f6f6; color: #555; border: 1px dashed #ddd; }
        .back-link { display: inline-block; margin-top: 20px; padding: 8px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        .back-link:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <div class="submissions-container">
        <h1>查看答卷</h1>
        <?php if (!empty($error_message)): ?>
            <div class="error-message-box"><?php echo $error_message; ?></div>
        <?php else: ?>
            <h2>问卷: <?php echo htmlspecialchars($questionnaire_title); ?> (ID: <?php echo htmlspecialchars($questionnaire_id); ?>)</h2>
            
            <?php if (!empty($processed_submissions)): ?>
                <table class="submissions-table">
                    <thead>
                        <tr>
                            <th style="width: 40px;">#</th>
                            <?php 
                            foreach ($question_headers as $header): 
                                $style = '';
                                $label = $header['label'];
                                // General approach: provide sensible defaults
                                if ($label === '学生姓名' || $label === '家长姓名') {
                                    $style = 'style="min-width: 90px; width: 110px;"';
                                } elseif ($label === '学生身份证号码') { 
                                    $style = 'style="min-width: 160px; width: 170px;"';
                                } elseif ($label === '家长手机') {
                                    $style = 'style="min-width: 100px; width: 110px;"';
                                } elseif (strpos($label, '学校') !== false) { 
                                    $style = 'style="min-width: 150px; width: 220px;"';
                                } elseif (strpos($label, '班级') !== false) { 
                                    $style = 'style="min-width: 80px; width: 100px;"';
                                }
                                // Other columns will auto-adjust or use default th styles
                            ?>
                                <th <?php echo $style; ?>><?php echo htmlspecialchars($label); ?></th>
                            <?php endforeach; ?>
                            <th style="width: 150px;">提交时间</th>
                            <th style="width: 130px;">IP 地址</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($processed_submissions as $index => $sub): ?>
                            <tr>
                                <td><?php echo $index + 1; ?></td>
                                <?php foreach ($question_headers as $header): ?>
                                    <td>
                                        <?php 
                                        if (isset($sub['answers'][$header['id']])) {
                                            $raw_answer = $sub['answers'][$header['id']];
                                            
                                            // Step 1: Decode HTML entities
                                            $decoded_answer = html_entity_decode($raw_answer, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            
                                            // Step 2: Trim each line using preg_replace_callback for multiline handling
                                            $cleaned_multiline_answer = preg_replace_callback('/^.*$/m', function ($matches) {
                                                $line_content_str = $matches; // This should be $matches
                                                // Trim leading/trailing Unicode whitespace from each line
                                                $trimmed_line = preg_replace('/^[\s\p{Z}]+|[\s\p{Z}]+$/u', '', $line_content_str);
                                                // Fallback if preg_replace fails on a specific line
                                                if ($trimmed_line === null) { 
                                                    $trimmed_line = trim($line_content_str); // Basic trim as a fallback
                                                }
                                                return $trimmed_line;
                                            }, $decoded_answer);
                                            
                                            // Fallback for preg_replace_callback itself (e.g. if $decoded_answer is not valid for regex)
                                            if ($cleaned_multiline_answer === null) {
                                                // Basic split, trim, implode as a less robust fallback
                                                $lines = explode("\n", $decoded_answer);
                                                $cleaned_lines = [];
                                                foreach ($lines as $line) {
                                                    $cleaned_lines[] = trim($line);
                                                }
                                                $cleaned_multiline_answer = implode("\n", $cleaned_lines);
                                            }

                                            // Step 3: Escape for HTML display
                                            $html_safe_answer = htmlspecialchars($cleaned_multiline_answer, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                            
                                            // Step 4: Convert newlines to <br>
                                            echo nl2br($html_safe_answer);
                                        } else {
                                            echo '-';
                                        }
                                        ?>
                                    </td>
                                <?php endforeach; ?>
                                <td><?php echo htmlspecialchars(date('Y-m-d H:i:s', strtotime($sub['submitted_at']))); ?></td>
                                <td><?php echo htmlspecialchars($sub['ip_address']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php elseif (empty($question_headers) && empty($error_message)): ?>
                 <p class="no-submissions-message">此问卷没有定义任何问题，因此无法显示答卷表。</p>
            <?php else: ?>
                <p class="no-submissions-message">此问卷目前还没有任何答卷记录。</p>
            <?php endif; ?>
        <?php endif; ?>
        <p style="text-align: center; margin-top: 30px;">
            <a href="dashboard.php?page=all_questionnaires" class="back-link">返回问卷列表</a>
            <?php if (!empty($processed_submissions) && empty($error_message)): ?>
            <a href="export_submissions.php?id=<?php echo htmlspecialchars($questionnaire_id); ?>" class="back-link" style="background-color: #28a745; margin-left: 10px;">导出到 Excel </a>
            <?php endif; ?>
        </p>
    </div>
</body>
</html>
