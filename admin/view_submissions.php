<?php
// 确保此文件被 dashboard.php 正确包含且用户已登录
if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME']) || !isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME'])) {
        echo "此页面不能直接访问。";
    }
    exit;
}

global $mysqli;

$questionnaire_id = $_GET['id'] ?? null;
$questionnaire_title = '';
$error_message = '';
$questions = []; // 存储问题信息，按sort_order排序
$submissions = []; // 存储答卷数据

// 分页参数
$page = max(1, intval($_GET['page'] ?? 1)); // 当前页码，最小为1
$per_page = 50; // 每页显示50条记录
$total_submissions = 0; // 总记录数
$total_pages = 0; // 总页数
$offset = ($page - 1) * $per_page; // 数据库查询偏移量

if (empty($questionnaire_id)) {
    $error_message = "错误：未提供问卷ID。";
} else {
    // 1. 获取问卷标题
    $stmt = $mysqli->prepare("SELECT title FROM questionnaires WHERE id = ?");
    if ($stmt) {
        $stmt->bind_param("s", $questionnaire_id);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            $questionnaire_title = $row['title'];
        } else {
            $error_message = "未找到指定的问卷。";
        }
        $stmt->close();
    } else {
        $error_message = "数据库查询失败。";
    }

    // 2. 获取问卷的所有问题（按顺序）
    if (empty($error_message)) {
        $stmt = $mysqli->prepare("SELECT id, label, type FROM questions WHERE questionnaire_id = ? ORDER BY sort_order ASC");
        if ($stmt) {
            $stmt->bind_param("s", $questionnaire_id);
            $stmt->execute();
            $result = $stmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $questions[] = $row;
            }
            $stmt->close();
        } else {
            $error_message = "获取问题列表失败。";
        }
    }

    // 3. 获取答卷总数（用于分页）
    if (empty($error_message)) {
        $stmt = $mysqli->prepare("SELECT COUNT(*) as total FROM submissions WHERE questionnaire_id = ?");
        if ($stmt) {
            $stmt->bind_param("s", $questionnaire_id);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($row = $result->fetch_assoc()) {
                $total_submissions = $row['total'];
                $total_pages = ceil($total_submissions / $per_page);
            }
            $stmt->close();
        } else {
            $error_message = "获取答卷总数失败。";
        }
    }

    // 4. 获取当前页的答卷数据
    if (empty($error_message) && $total_submissions > 0) {
        $stmt = $mysqli->prepare("SELECT id, submitted_at, ip_address FROM submissions WHERE questionnaire_id = ? ORDER BY submitted_at DESC LIMIT ? OFFSET ?");
        if ($stmt) {
            $stmt->bind_param("sii", $questionnaire_id, $per_page, $offset);
            $stmt->execute();
            $result = $stmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $submission_id = $row['id'];
                $answers = [];

                // 获取该答卷的所有答案
                $stmt_ans = $mysqli->prepare("SELECT question_id, answer_value FROM submission_answers WHERE submission_id = ?");
                if ($stmt_ans) {
                    $stmt_ans->bind_param("i", $submission_id);
                    $stmt_ans->execute();
                    $result_ans = $stmt_ans->get_result();
                    while ($ans_row = $result_ans->fetch_assoc()) {
                        $answers[$ans_row['question_id']] = $ans_row['answer_value'];
                    }
                    $stmt_ans->close();
                }

                $submissions[] = [
                    'id' => $submission_id,
                    'submitted_at' => $row['submitted_at'],
                    'ip_address' => $row['ip_address'],
                    'answers' => $answers
                ];
            }
            $stmt->close();
        } else {
            $error_message = "获取答卷数据失败。";
        }
    }
}
?>
<style>
    body {
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 20px;
        color: #333;
    }

    .container {
        max-width: 1400px;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .header {
        background-color: #0050b3;
        color: white;
        padding: 20px 30px;
        text-align: center;
    }

    .header h1 {
        margin: 0;
        font-size: 24px;
        font-weight: normal;
    }

    .header .questionnaire-info {
        margin-top: 10px;
        font-size: 16px;
        opacity: 0.9;
    }

    .content {
        padding: 30px;
    }

    .error-message {
        background-color: #fff2f0;
        border: 1px solid #ffccc7;
        color: #cf1322;
        padding: 15px;
        border-radius: 4px;
        text-align: center;
        margin-bottom: 20px;
    }

    .no-data-message {
        text-align: center;
        color: #666;
        font-size: 16px;
        padding: 40px;
        background-color: #fafafa;
        border-radius: 4px;
        border: 1px dashed #d9d9d9;
    }

    .table-container {
        overflow-x: auto;
        margin-bottom: 30px;
    }

    .submissions-table {
        width: 100%;
        border-collapse: collapse;
        min-width: 800px;
    }

    .submissions-table th {
        background-color: #fafafa;
        border: 1px solid #e8e8e8;
        padding: 12px 8px;
        text-align: center;
        font-weight: bold;
        color: #262626;
        white-space: nowrap;
    }

    .submissions-table td {
        border: 1px solid #e8e8e8;
        padding: 10px 8px;
        text-align: center;
        vertical-align: middle;
        word-wrap: break-word;
        max-width: 200px;
    }

    .submissions-table tbody tr:nth-child(even) {
        background-color: #fafafa;
    }

    .submissions-table tbody tr:hover {
        background-color: #e6f7ff;
    }

    .serial-number {
        width: 60px;
        font-weight: bold;
        color: #0050b3;
    }

    .ip-column {
        width: 120px;
        font-family: monospace;
    }

    .time-column {
        width: 150px;
        font-size: 12px;
    }

    .buttons {
        text-align: center;
        padding: 20px 0;
        border-top: 1px solid #e8e8e8;
        background-color: #fafafa;
    }

    .btn {
        display: inline-block;
        padding: 10px 20px;
        margin: 0 10px;
        text-decoration: none;
        border-radius: 4px;
        font-size: 14px;
        font-weight: bold;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }

    .btn-primary {
        background-color: #0050b3;
        color: white;
    }

    .btn-primary:hover {
        background-color: #003a8c;
    }

    .btn-success {
        background-color: #52c41a;
        color: white;
    }

    .btn-success:hover {
        background-color: #389e0d;
    }

    .stats {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f0f9ff;
        border-radius: 4px;
        border-left: 4px solid #0050b3;
    }

    .stats-text {
        color: #0050b3;
        font-weight: bold;
    }

    .pagination {
        text-align: center;
        margin: 20px 0;
        padding: 20px 0;
        border-top: 1px solid #e8e8e8;
    }

    .pagination-info {
        margin-bottom: 15px;
        color: #666;
        font-size: 14px;
    }

    .pagination-links {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
        flex-wrap: wrap;
    }

    .pagination-links a,
    .pagination-links span {
        display: inline-block;
        padding: 8px 12px;
        margin: 2px;
        text-decoration: none;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
        font-size: 14px;
        min-width: 40px;
        text-align: center;
    }

    .pagination-links a:hover {
        background-color: #e6f7ff;
        border-color: #0050b3;
        color: #0050b3;
    }

    .pagination-links .current {
        background-color: #0050b3;
        border-color: #0050b3;
        color: white;
        font-weight: bold;
    }

    .pagination-links .disabled {
        color: #ccc;
        cursor: not-allowed;
        background-color: #f5f5f5;
    }

    .pagination-links .disabled:hover {
        background-color: #f5f5f5;
        border-color: #d9d9d9;
        color: #ccc;
    }
</style>

<div class="container">
    <div class="header">
        <h1>查看答卷</h1>
        <?php if (!empty($questionnaire_title)): ?>
            <div class="questionnaire-info">
                问卷：<?php echo htmlspecialchars($questionnaire_title); ?>
                <?php if (!empty($questionnaire_id)): ?>
                    (ID: <?php echo htmlspecialchars($questionnaire_id); ?>)
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <div class="content">
        <?php if (!empty($error_message)): ?>
            <div class="error-message">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php elseif (empty($questions)): ?>
            <div class="no-data-message">
                此问卷没有定义任何问题。
            </div>
        <?php elseif (empty($submissions)): ?>
            <div class="no-data-message">
                此问卷目前还没有任何答卷记录。
            </div>
        <?php else: ?>
            <div class="stats">
                <span class="stats-text">
                    共收到 <?php echo $total_submissions; ?> 份答卷
                    <?php if ($total_pages > 1): ?>
                        （第 <?php echo $page; ?> 页，共 <?php echo $total_pages; ?> 页）
                    <?php endif; ?>
                </span>
            </div>

            <div class="table-container">
                <table class="submissions-table">
                    <thead>
                        <tr>
                            <th class="serial-number">序号</th>
                            <?php foreach ($questions as $question): ?>
                                <th><?php echo htmlspecialchars($question['label']); ?></th>
                            <?php endforeach; ?>
                            <th class="ip-column">IP地址</th>
                            <th class="time-column">提交时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($submissions as $index => $submission): ?>
                            <tr>
                                <td class="serial-number"><?php echo $offset + $index + 1; ?></td>
                                <?php foreach ($questions as $question): ?>
                                    <td>
                                        <?php
                                        if (isset($submission['answers'][$question['id']])) {
                                            echo htmlspecialchars($submission['answers'][$question['id']]);
                                        } else {
                                            echo '-';
                                        }
                                        ?>
                                    </td>
                                <?php endforeach; ?>
                                <td class="ip-column"><?php echo htmlspecialchars($submission['ip_address']); ?></td>
                                <td class="time-column"><?php echo date('Y-m-d H:i:s', strtotime($submission['submitted_at'])); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <div class="pagination-info">
                        显示第 <?php echo $offset + 1; ?> - <?php echo min($offset + $per_page, $total_submissions); ?> 条，共 <?php echo $total_submissions; ?> 条记录
                    </div>
                    <div class="pagination-links">
                        <?php
                        // 上一页链接
                        if ($page > 1) {
                            echo '<a href="dashboard.php?page=view_submissions&id=' . urlencode($questionnaire_id) . '&page=' . ($page - 1) . '">上一页</a>';
                        } else {
                            echo '<span class="disabled">上一页</span>';
                        }

                        // 页码链接
                        $start_page = max(1, $page - 5);
                        $end_page = min($total_pages, $page + 5);

                        if ($start_page > 1) {
                            echo '<a href="dashboard.php?page=view_submissions&id=' . urlencode($questionnaire_id) . '&page=1">1</a>';
                            if ($start_page > 2) {
                                echo '<span class="disabled">...</span>';
                            }
                        }

                        for ($i = $start_page; $i <= $end_page; $i++) {
                            if ($i == $page) {
                                echo '<span class="current">' . $i . '</span>';
                            } else {
                                echo '<a href="dashboard.php?page=view_submissions&id=' . urlencode($questionnaire_id) . '&page=' . $i . '">' . $i . '</a>';
                            }
                        }

                        if ($end_page < $total_pages) {
                            if ($end_page < $total_pages - 1) {
                                echo '<span class="disabled">...</span>';
                            }
                            echo '<a href="dashboard.php?page=view_submissions&id=' . urlencode($questionnaire_id) . '&page=' . $total_pages . '">' . $total_pages . '</a>';
                        }

                        // 下一页链接
                        if ($page < $total_pages) {
                            echo '<a href="dashboard.php?page=view_submissions&id=' . urlencode($questionnaire_id) . '&page=' . ($page + 1) . '">下一页</a>';
                        } else {
                            echo '<span class="disabled">下一页</span>';
                        }
                        ?>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <div class="buttons">
        <a href="dashboard.php?page=all_questionnaires" class="btn btn-primary">返回问卷列表</a>
        <?php if (!empty($submissions) && empty($error_message)): ?>
            <a href="export_submissions.php?id=<?php echo urlencode($questionnaire_id); ?>" class="btn btn-success">导出到Excel</a>
        <?php endif; ?>
    </div>
</div>
