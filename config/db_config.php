<?php
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', 'ServBay.dev');
define('DB_NAME', 'questionnaire_system');

/* 连接数据库 */
$mysqli = new mysqli(DB_SERVER, DB_USERNAME, DB_PASSWORD);

// 检查连接
if ($mysqli->connect_error) {
    die("连接失败: " . $mysqli->connect_error);
}

// 尝试选择数据库，如果不存在则创建
if (!$mysqli->select_db(DB_NAME)) {
    $sql_create_db = "CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    if ($mysqli->query($sql_create_db) === TRUE) {
        // echo "数据库 " . DB_NAME . " 创建成功或已存在<br>";
        $mysqli->select_db(DB_NAME);
    } else {
        die("创建数据库错误: " . $mysqli->error);
    }
}

// 设置字符集为 utf8mb4
$mysqli->set_charset("utf8mb4");

// 将 $link 替换为 $mysqli 以便全局使用（如果需要的话）
// 或者在需要的地方直接使用 $mysqli
// 为了与原有代码兼容，可以赋值给 $link
$link = $mysqli;

// 创建数据表 (如果不存在)
$create_questionnaires_table_sql = "
CREATE TABLE IF NOT EXISTS `questionnaires` (
  `id` VARCHAR(10) NOT NULL PRIMARY KEY,
  `title` VARCHAR(255) NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `status` ENUM('draft', 'published', 'archived', 'deleted') DEFAULT 'draft'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

$create_questions_table_sql = "
CREATE TABLE IF NOT EXISTS `questions` (
  `id` VARCHAR(50) NOT NULL PRIMARY KEY,
  `questionnaire_id` VARCHAR(10) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `label` TEXT NOT NULL,
  `options` TEXT DEFAULT NULL,
  `sort_order` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`questionnaire_id`) REFERENCES `questionnaires`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

if (!$mysqli->query($create_questionnaires_table_sql)) {
    die("创建 questionnaires 表错误: " . $mysqli->error);
}

if (!$mysqli->query($create_questions_table_sql)) {
    die("创建 questions 表错误: " . $mysqli->error);
}

// 创建存储提交记录的表
$create_submissions_table_sql = "
CREATE TABLE IF NOT EXISTS `submissions` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `questionnaire_id` VARCHAR(10) NOT NULL,
  `submitted_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `ip_address` VARCHAR(45) DEFAULT NULL,
  FOREIGN KEY (`questionnaire_id`) REFERENCES `questionnaires`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

// 创建存储具体答案的表
$create_submission_answers_table_sql = "
CREATE TABLE IF NOT EXISTS `submission_answers` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `submission_id` INT NOT NULL,
  `question_id` VARCHAR(50) NOT NULL, /* Corresponds to questions.id */
  `answer_value` TEXT DEFAULT NULL,
  FOREIGN KEY (`submission_id`) REFERENCES `submissions`(`id`) ON DELETE CASCADE
  /* No direct FK to questions.id to allow questions to be deleted/modified without breaking old submissions if needed,
     or add ON DELETE SET NULL / ON DELETE RESTRICT if strict integrity is required and question modifications are handled carefully.
     For simplicity, we'll omit direct FK here or assume questions are not hard-deleted if submissions exist.
     Alternatively, store question label/type snapshot here too.
  */
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

if (!$mysqli->query($create_submissions_table_sql)) {
    die("创建 submissions 表错误: " . $mysqli->error);
}

if (!$mysqli->query($create_submission_answers_table_sql)) {
    die("创建 submission_answers 表错误: " . $mysqli->error);
}

?>
