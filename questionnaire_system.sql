-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-06-22 16:12:47
-- 服务器版本： 5.7.44-log
-- PHP 版本： 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `questionnaire_system`
--

-- --------------------------------------------------------

--
-- 表的结构 `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `admin_users`
--

INSERT INTO `admin_users` (`username`, `password`) VALUES
('admin', '21232f297a57a5a743894a0e4a801fc3'),
('Michael', '21232f297a57a5a743894a0e4a801fc3');

-- --------------------------------------------------------

--
-- 表的结构 `questionnaires`
--

CREATE TABLE `questionnaires` (
  `id` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('draft','published','archived','deleted') COLLATE utf8mb4_unicode_ci DEFAULT 'draft',
  `submission_limit` int(11) DEFAULT NULL COMMENT '回收份数限制，NULL表示无限制'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `questionnaires`
--

INSERT INTO `questionnaires` (`id`, `title`, `created_at`, `status`) VALUES
('1750601155', '更新测试问卷标题', '2025-06-22 14:07:40', 'draft'),
('1750608150', '再来测试一个', '2025-06-22 16:04:04', 'draft'),
('1750608397', '再来测试一个一', '2025-06-22 16:06:48', 'draft');

-- --------------------------------------------------------

--
-- 表的结构 `questions`
--

CREATE TABLE `questions` (
  `id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `questionnaire_id` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `label` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` text COLLATE utf8mb4_unicode_ci,
  `sort_order` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `questions`
--

INSERT INTO `questions` (`id`, `questionnaire_id`, `type`, `label`, `options`, `sort_order`, `created_at`) VALUES
('q_68580df53ba21', '1750601155', 'single_choice', '学校', '[\"江苏省太仓高级中学\",\"太仓市实验高级中学\",\"江苏省沙溪高级中学\",\"太仓市明德高级中学\"]', 1, '2025-06-22 15:23:44'),
('q_68580e07aa293', '1750601155', 'name', '学生姓名', NULL, 2, '2025-06-22 15:23:44'),
('q_68580e0dbb96e', '1750601155', 'id_card', '学生身份证号码', NULL, 3, '2025-06-22 15:23:44'),
('q_68580e19ae4c3', '1750601155', 'name', '家长姓名', NULL, 4, '2025-06-22 15:23:44'),
('q_68580e21073e5', '1750601155', 'phone', '家长手机', NULL, 5, '2025-06-22 15:23:44'),
('q_685829485ab35', '1750608150', 'single_choice', '学校', '[\"江苏省太仓高级中学\",\"太仓市实验高级中学\",\"江苏省沙溪高级中学\",\"太仓市明德高级中学\"]', 1, '2025-06-22 16:04:04'),
('q_685829535a997', '1750608150', 'name', '学生姓名', NULL, 2, '2025-06-22 16:04:04'),
('q_6858295b404de', '1750608150', 'id_card', '学生身份证号码', NULL, 3, '2025-06-22 16:04:04'),
('q_685829640a3c1', '1750608150', 'name', '家长姓名', NULL, 4, '2025-06-22 16:04:04'),
('q_6858296d682ff', '1750608150', 'phone', '家长手机', NULL, 5, '2025-06-22 16:04:04'),
('q_68582a12ee928', '1750608397', 'name', '姓名', NULL, 2, '2025-06-22 16:06:48'),
('q_68582a1492ed0', '1750608397', 'id_card', '身份证号码', NULL, 1, '2025-06-22 16:06:48');

-- --------------------------------------------------------

--
-- 表的结构 `submissions`
--

CREATE TABLE `submissions` (
  `id` int(11) NOT NULL,
  `questionnaire_id` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `submitted_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `submissions`
--

INSERT INTO `submissions` (`id`, `questionnaire_id`, `submitted_at`, `ip_address`) VALUES
(1, '1750601155', '2025-06-22 14:24:15', '127.0.0.1'),
(2, '1750601155', '2025-06-22 14:31:08', '127.0.0.1'),
(3, '1750601155', '2025-06-22 15:22:49', '127.0.0.1');

-- --------------------------------------------------------

--
-- 表的结构 `submission_answers`
--

CREATE TABLE `submission_answers` (
  `id` int(11) NOT NULL,
  `submission_id` int(11) NOT NULL,
  `question_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `answer_value` text COLLATE utf8mb4_unicode_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `submission_answers`
--

INSERT INTO `submission_answers` (`id`, `submission_id`, `question_id`, `answer_value`) VALUES
(1, 1, 'q_68580df53ba21', '江苏省太仓高级中学'),
(2, 1, 'q_68580e07aa293', '学生姓名一'),
(3, 1, 'q_68580e0dbb96e', '320522197812120012'),
(4, 1, 'q_68580e19ae4c3', '家长姓名一'),
(5, 1, 'q_68580e21073e5', '13004541361'),
(6, 2, 'q_68580df53ba21', '太仓市实验高级中学'),
(7, 2, 'q_68580e07aa293', '学生姓名二'),
(8, 2, 'q_68580e0dbb96e', '320522197812120011'),
(9, 2, 'q_68580e19ae4c3', '家长姓名二'),
(10, 2, 'q_68580e21073e5', '13004541362'),
(11, 3, 'q_68580df53ba21', '江苏省沙溪高级中学'),
(12, 3, 'q_68580e07aa293', '学生姓名三'),
(13, 3, 'q_68580e0dbb96e', '320522197812120012'),
(14, 3, 'q_68580e19ae4c3', '家长姓名三'),
(15, 3, 'q_68580e21073e5', '13004541363');

--
-- 转储表的索引
--

--
-- 表的索引 `questionnaires`
--
ALTER TABLE `questionnaires`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `questions`
--
ALTER TABLE `questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `questionnaire_id` (`questionnaire_id`);

--
-- 表的索引 `submissions`
--
ALTER TABLE `submissions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `questionnaire_id` (`questionnaire_id`);

--
-- 表的索引 `submission_answers`
--
ALTER TABLE `submission_answers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `submission_id` (`submission_id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `submissions`
--
ALTER TABLE `submissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- 使用表AUTO_INCREMENT `submission_answers`
--
ALTER TABLE `submission_answers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- 限制导出的表
--

--
-- 限制表 `questions`
--
ALTER TABLE `questions`
  ADD CONSTRAINT `questions_ibfk_1` FOREIGN KEY (`questionnaire_id`) REFERENCES `questionnaires` (`id`) ON DELETE CASCADE;

--
-- 限制表 `submissions`
--
ALTER TABLE `submissions`
  ADD CONSTRAINT `submissions_ibfk_1` FOREIGN KEY (`questionnaire_id`) REFERENCES `questionnaires` (`id`) ON DELETE CASCADE;

--
-- 限制表 `submission_answers`
--
ALTER TABLE `submission_answers`
  ADD CONSTRAINT `submission_answers_ibfk_1` FOREIGN KEY (`submission_id`) REFERENCES `submissions` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
